using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Infrastructure.Data;
using Infrastructure.Services;
using Infrastructure.Repositories;
using Infrastructure.Data.Repositories;
using Infrastructure.Data.Repositories.Interfaces;
using Application.Contracts.Interfaces;
using Application.Contracts.Services;
using Application.Contracts.Repositories;
using Core.Interfaces;
using Core.Entities;
using Microsoft.AspNetCore.Identity;
using WebApi.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Environment variables configuration
builder.Configuration.AddEnvironmentVariables();

// Add services to the container.
var configuration = builder.Configuration;
builder.Services.AddOpenApi();

// Memory Cache
builder.Services.AddMemoryCache();

// Database configuration
builder.Services.AddDbContext<B2BDbContext>(options =>
    options.UseNpgsql(configuration.GetConnectionString("VeriTabani")));

// CORS yapılandırması - müşteri tarafı için
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowCustomerFrontend", policy =>
    {
        policy
            .WithOrigins(configuration.GetSection("CorsHosts").Get<string[]>() ?? 
                        new[] { "http://localhost:3000", "http://localhost:3001" })
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials()
            .WithExposedHeaders("Content-Disposition");
    });
});

// JWT Bearer Authentication - müşteri token'ları için
builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = false,
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["NextAuth:Secret"]!)),
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(5), 
            NameClaimType = "name",
            RoleClaimType = "roles"
        };
    });

// HttpContextAccessor ve CurrentUserService kayıtları
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

// Password Hasher for Customer
builder.Services.AddScoped<IPasswordHasher<Customer>, PasswordHasher<Customer>>();

// Repository kayıtları
builder.Services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
builder.Services.AddScoped<ICustomerRepository, CustomerRepository>();
builder.Services.AddScoped<ICartRepository, CartRepository>();
builder.Services.AddScoped<ICartItemRepository, CartItemRepository>();

// Encryption Service
builder.Services.AddScoped<IEncryptionService, EncryptionService>();

// Cache Service
builder.Services.AddScoped<ICacheService, CacheService>();

// B2C Customer Services kayıtları
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ICustomerProductService, CustomerProductService>();
builder.Services.AddScoped<ICustomerCategoryService, CustomerCategoryService>();
builder.Services.AddScoped<IProductVolumeService, ProductVolumeService>();
builder.Services.AddScoped<IAddressService, AddressService>();
builder.Services.AddScoped<IAddressRepository, AddressRepository>();
// Customer Authentication Services
builder.Services.AddScoped<ICustomerAuthService, CustomerAuthService>();
builder.Services.AddScoped<IProductReviewService, ProductReviewService>();
// Campaign Services
builder.Services.AddScoped<ICampaignRepository, CampaignRepository>();
builder.Services.AddScoped<ICampaignService, CampaignService>();
builder.Services.AddScoped<ICampaignCalculationService, CampaignCalculationService>();

// Pricing Services
builder.Services.AddScoped<IPricingCalculationService, PricingCalculationService>();

// Company Info Services
builder.Services.AddScoped<ICompanyInfoService, CompanyInfoService>();
builder.Services.AddScoped<ICompanyInfoRepository, CompanyInfoRepository>();
builder.Services.AddScoped<ISystemSettingsService, SystemSettingsService>();
builder.Services.AddScoped<ISystemSettingsRepository, SystemSettingsRepository>();
// SEO Services
builder.Services.AddScoped<IProductCategorySeoService, ProductCategorySeoService>();

// Mail Services
builder.Services.AddScoped<IMailLinkService, MailLinkService>();

builder.Services.AddScoped<ICustomerWishListService, CustomerWishListService>();

// Payments - providers and factory
builder.Services.AddScoped<Payments.Abstractions.IPaymentServiceFactory, Payments.Implementation.PaymentServiceFactory>();
builder.Services.AddScoped<IPaymentProviderRepository, PaymentProviderRepository>();
builder.Services.AddScoped<IPaymentProviderService, PaymentProviderService>();
builder.Services.AddScoped<IPaymentService, PaymentService>();
builder.Services.AddScoped<IUserPointService, UserPointService>();
builder.Services.AddScoped<IUserPointRepository, UserPointRepository>();
builder.Services.AddScoped<ICouponRepository, CouponRepository>();
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<ICartService, CartService>();
builder.Services.AddControllers();

// In-memory payment session store to correlate payment token -> customer/cart
builder.Services.AddSingleton<WebApi.Services.IPaymentSessionStore, WebApi.Services.InMemoryPaymentSessionStore>();

// MassTransit configuration for event publishing
builder.Services.AddMassTransitConfiguration(configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// CORS middleware
app.UseCors("AllowCustomerFrontend");
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Seeders
using (var scope = app.Services.CreateScope())
{
    var sp = scope.ServiceProvider;
    var env = sp.GetRequiredService<IHostEnvironment>();
    var logger = sp.GetRequiredService<ILoggerFactory>();

    try
    {
        var providerSeeder = ActivatorUtilities.CreateInstance<Infrastructure.Seeders.PaymentProviderSeeder>(sp);
        await providerSeeder.SeedAsync();
    }
    catch (Exception ex)
    {
        logger.CreateLogger("Seeder").LogError(ex, "Payment provider seeding failed");
    }
}

app.Run();
