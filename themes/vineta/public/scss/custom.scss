@keyframes infiniteslide17418080312321156 {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-1440px, 0, 0);
  }
}

/* Cart Summary Styles */
.cart-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  span:first-child {
    font-weight: 500;
  }

  span:last-child {
    font-weight: 600;
  }
}

/* Sipariş durumu sayfası stilleri */
.cart-summary {
  margin: 16px 0;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.price-info {
  text-align: right;
}

.text-success {
  color: #28a745 !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.price-discount {
  color: #28a745 !important;
}
.tf-brand {
  &:hover {
    animation-play-state: paused !important;
  }
}
// .sw-height .swiper-slide {
//   height: 100% !important;
// }
.menuActive {
  color: var(--primary) !important;
}
.rc-slider .rc-slider-handle {
  border: solid 2px var(--primary);
  opacity: 1 !important;
  background-color: #fff !important;
  width: 20px;
  border-radius: 10px !important;
  height: 20px;

  margin-top: -9px;
}

.rc-slider .rc-slider-handle:hover {
  border-color: var(--primary) !important;
}

.rc-slider .rc-slider-handle-dragging {
  border-color: var(--primary) !important !important;
  box-shadow: none !important;
}

.rc-slider .rc-slider-track,
.rc-slider .rc-slider-tracks {
  background-color: var(--primary) !important;
}
.image-compare {
  border-radius: 16px !important;
  overflow: hidden;
  button {
    transition: 0s !important;
  }
}
.zoom-magnifier-containing .drift-zoom-pane {
  border-radius: 50%;
  width: 150px !important;
  height: 150px !important;
  overflow: hidden;
}
// .swiper-horizontal {
//   height: 100%;
//   .swiper-slide {
//     height: auto !important;
//     min-height: 100%;
//   }
// }
.cursor-pointer {
  cursor: pointer;
}
.tfSubscribeMsg {
  display: none;
  max-height: 0px;
  // transition: 0.4s;
  overflow: hidden;
  &.active {
    margin-top: 10px;
    margin-bottom: 10px;
    max-height: 180px;
    display: block;
  }
}
@media only screen and (max-width: 767px) {
  .footer-col-block .tf-collapse-content {
    overflow-y: hidden;
    display: block;
    height: 0;
    transition: 0.5s;
  }
}
.home-phonecase {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.slider-fashion-1 .swiper-fade .swiper-slide:not(.swiper-slide-active) {
  opacity: 0 !important;
}
.thumbs-slider {
  .flat-wrap-media-product {
    .swiper {
      height: 100% !important;
      .item {
        height: 100% !important;
        img {
          height: 100% !important;
          object-fit: cover;
        }
      }
    }
  }
}
.infiniteslider {
  display: flex;
  flex-flow: row;
  align-items: center;
  animation: 1.85078s linear 0s infinite normal none running infiniteslide;
  &:hover {
    animation-play-state: paused;
  }
}
@keyframes infiniteslide {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(-148.0625px, 0, 0);
  }
}

// Varyant seçimi stilleri
.size-box {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;

  .size-item {
    padding: 6px 12px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    color: #333 !important;
    font-size: 12px;
    font-weight: 500;
    min-width: 32px;
    text-align: center;
    position: relative;

    &:hover {
      border-color: var(--primary, #ff6f61);
      background-color: rgba(255, 111, 97, 0.1);
      color: var(--primary, #ff6f61);
    }

    &.active {
      border-color: var(--primary, #ff6f61);
      background-color: var(--primary, #ff6f61);
      color: white !important;
    }

    .tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background-color: #333;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      z-index: 10;
      margin-bottom: 5px;

      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: #333;
      }
    }

    &:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }
  }
}

.list-color-product {
  .list-color-item {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }

    &.active {
      transform: scale(1.1);
      box-shadow: 0 0 0 2px var(--primary, #ff6f61);
    }
  }
}

/* Star Rating Component */
.star-rating {
  display: flex;
  gap: 4px;
  align-items: center;
}

.star-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  .icon-star {
    font-size: 20px;
    color: #e5e7eb; /* Gray color for inactive stars */
    transition: color 0.2s ease;
  }

  &:hover .icon-star {
    color: #fbbf24; /* Yellow color on hover */
  }

  &.active .icon-star {
    color: #f59e0b; /* Active yellow color */
  }

  &:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
  }
}
