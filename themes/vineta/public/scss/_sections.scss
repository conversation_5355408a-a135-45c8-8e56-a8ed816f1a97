@use "./abstracts/index" as *;

.s-blog-list-v1,
.s-blog-list-v2 {
  .blog-item {
    margin-bottom: 40px;
    &:last-of-type {
      margin-bottom: 60px;
    }
  }
}
.s-blog-list-grid {
  gap: 42px 24px;
  &.grid-2 {
    .blog-item .entry_image {
      aspect-ratio: 464 / 395;
    }
  }
}
.s-blog-single {
  .heading {
    gap: 20px;
    text-align: center;
    margin-bottom: 48px;
    > .entry-tag,
    > .entry-meta {
      justify-content: center;
    }
  }
  .content {
    margin-bottom: 80px;
    .entry_image {
      border-radius: 16px;
      overflow: hidden;
      margin-bottom: 42px;
    }
    .text {
      font-size: 16px;
      line-height: 24px;
      color: var(--text);
      max-width: 1008px;
      width: 100%;
      margin: 0px auto;
      margin-bottom: 42px;
      text-align: center;
    }
  }
  .group-image {
    @include flex(center, center);
    gap: 24px;
    margin-bottom: 65px;
    .entry_image {
      margin-bottom: 0 !important;
    }
  }
  .entry-social {
    display: flex;
    align-items: center;
    gap: 15px;
    > p {
      font-size: 16px;
      font-weight: 500;
      line-height: 25px;
    }
  }
  .bot {
    margin-bottom: 42px;
    .entry-tag {
      margin-bottom: 20px;
    }
  }
  .related-post {
    @include flex(center, space-between);
    padding: 32px 0px 42px;
    border-top: 1px solid var(--line);
    border-bottom: 1px solid var(--line);
    gap: 30px;
    .post {
      display: flex;
      gap: 20px;
      align-items: center;
      &:hover {
        .icon {
          background-color: var(--primary);
          color: var(--white);
        }
        .name-post {
          color: var(--primary);
        }
      }
    }
    p {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 15px;
      &.name-post {
        font-size: 16px;
        line-height: 19px;
        margin-bottom: 0px;
        @include transition3;
      }
    }
    .icon {
      width: 44px;
      height: 44px;
      @include flex(center, center);
      border-radius: 50%;
      border: 1px solid var(--line);
      @include transition3;
      flex-shrink: 0;
    }
    .text-wrap-left {
      text-align: left;
    }
    .text-wrap-right {
      text-align: right;
    }
  }
}
.block-quote {
  padding: 30px;
  border-radius: 16px;
  background-color: #eeffde;
  min-height: 230px;
  display: flex;
  align-items: center;
  margin-bottom: 42px;
  p {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    max-width: 1008px;
    width: 100%;
    margin: 0px auto;
    text-align: center;
  }
}
.sb-contact {
  border-radius: 16px;
  background-color: var(--surface);
  padding: 40px;
  .title {
    font-size: 24px;
    font-weight: 500;
    line-height: 29px;
    margin-bottom: 30px;
  }
  .sub {
    color: var(--text);
    line-height: 22px;
    margin-bottom: 28px;
  }
  .btn-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 252px;
    width: 100%;
    margin: 0px auto;
  }
  .tf-btn {
    text-transform: none;
    font-family: $font-4;
    font-weight: 600;
  }
}
.s-faq {
  .faq-item {
    &:not(:last-child) {
      margin-bottom: 52px;
    }
  }
  .name-faq {
    font-size: 24px;
    font-weight: 500;
    line-height: 29px;
    margin-bottom: 32px;
  }
}
.s-term-user {
  .content {
    display: grid;
    gap: 52px;
    max-width: 1008px;
    width: 100%;
    margin: 0px auto;
  }
  .term-title {
    font-size: 24px;
    font-weight: 500;
    line-height: 29px;
    color: var(--dark);
    margin-bottom: 24px;
  }
  .term-text {
    span {
      font-weight: 500;
      color: var(--dark);
    }
  }
  .term-subtitle {
    font-size: 18px;
    font-weight: 500;
    line-height: 22px;
    color: var(--dark);
    margin-bottom: 12px;
  }
  .text-wrap {
    display: grid;
    gap: 24px;
  }
}
.tf-page-title {
  padding: 52px 0px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url(/images/banner/banner-dermedic.png);
  .box-title {
    display: grid;
    gap: 12px;
    max-width: 560px;
    margin: auto;
  }
  .breadcrumb-list {
    justify-content: center;
  }
}
.breadcrumb-list {
  display: flex;
  gap: 8px;
  align-items: center;
  .breadcrumb-item {
    color: #757575;
    &::before {
      content: none;
    }
  }
  .dot {
    width: 16px;
    height: 16px;
    @include flex(center, center);
    span {
      width: 4px;
      height: 4px;
      display: inline-block;
      background-color: #757575;
      border-radius: 50%;
    }
  }
  .current {
    color: var(--dark);
  }
}
.breadcrumb-wrap {
  padding: 32px 0px 27px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.breadcrumb-prev-next {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--dark);
  .breadcrumb-back {
    font-size: 20px;
  }
  .breadcrumb-next,
  .breadcrumb-prev {
    width: 18px;
    height: 18px;
    font-size: 14px;
    @include flex(center, center);
  }
}
.s-404 {
  padding: 100px 0px;
}
.wg-404 {
  @include flex(center, center);
  flex-direction: column;
  .image {
    max-width: 236px;
    width: 100%;
    margin-bottom: 12px;
  }
  .title {
    margin-bottom: 12px;
  }
  .sub {
    margin-bottom: 32px;
  }
}
.wg-countdown {
  .countdown__timer {
    display: flex;
    gap: 20px;
  }
  .countdown__item {
    display: inline-flex;
    flex-direction: column;
    position: relative;
    width: 90px;
    height: 90px;
    text-align: center;
    justify-content: center;
    border-radius: 6px;
    border: 1px solid var(--primary);
  }
  .countdown__value {
    display: inline-block;
    font-size: 36px;
    font-weight: 500;
    line-height: 44px;
    letter-spacing: -0.02em;
    .countdown__value--3 {
      width: 100%;
      max-width: 90px;
    }
  }
}
.wg-countdown-2 {
  width: max-content;
  padding: 16px 32px;
  border-radius: 8px;
  border: 1px solid var(--line);
  background-color: var(--white);
  &.color-1 {
    .countdown__value {
      color: #8f6dff;
    }
  }

  .countdown__value {
    padding-top: 0px;
    font-family: $font-6;
    font-size: 40px;
    font-weight: 500;
    line-height: 50px;
    color: var(--primary);
  }
  .countdown__timer {
    display: flex;
    gap: 29px;
  }
  .countdown__item {
    display: flex;
    flex-direction: column;
    text-align: center;
    gap: 4px;
    min-width: 56px;
    &:not(:last-child) {
      .countdown__value {
        position: relative;
        &::after {
          content: ":";
          position: absolute;
          top: 50%;
          transform: translate(50%, -50%);
          right: -14px;
          font-family: $font-main;
          font-size: 20px;
          font-weight: 500;
          line-height: 30px;
          color: var(--dark);
        }
      }
    }
    &:first-child {
      min-width: 30px;
    }
  }
}
.wg-coming-soon {
  @include flex(center, center);
  flex-direction: column;
  padding-top: 80px;
  padding-bottom: 100px;
  .title {
    margin-bottom: 12px;
  }
  .sub {
    margin-bottom: 48px;
  }
  .wg-countdown {
    margin-bottom: 48px;
  }
  .form-email-wrap {
    max-width: 482px;
    width: 100%;
    margin-bottom: 64px;
    .button-submit {
      right: 5px;
    }
  }
}
.wg-map {
  &.style-absolute {
    position: relative;
    .box-store {
      position: absolute;
      left: 30px;
      top: 50%;
      transform: translateY(-50%);
      right: 15px;
      max-width: 419px;
      &:hover {
        background-color: var(--white);
      }
    }
  }
  .map {
    border-radius: 16px;
    overflow: hidden;
    height: 589px;
    width: 100%;
  }
}
.s-contact {
  .content-right,
  .content-left {
    display: grid;
    gap: 20px;
  }
  .wg-map {
    margin-bottom: 91px;
  }
  .sub-title {
    font-size: 16px;
    line-height: 26px;
  }
  &.style-2 {
    display: flex;
    justify-content: center;
    flex-wrap: wrap-reverse;
    gap: 15px;
    .content-left {
      padding: 30px;
      border-radius: 16px;
      position: relative;
      z-index: 2;
      gap: 15px;
      margin-bottom: 0;
      justify-items: flex-start;
      .tf-btn {
        padding: 10px 24px;
        text-transform: none;
        margin-top: 20px;
      }
      .tf-social-icon {
        .social-item:not(:hover) {
          background-color: transparent;
          border-color: var(--white);
        }
      }
    }
    .image-right {
      border-radius: 16px;
      overflow: hidden;
      img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }
}
.contact-list {
  display: grid;
  gap: 10px;
  p {
    font-size: 16px;
    font-weight: 500;
    line-height: 26px;
  }
  a,
  span {
    font-weight: 400;
    color: var(--text);
  }
}
.box-store {
  padding: 40px 24px 20px;
  border-radius: 16px;
  border: 1px solid var(--line);
  background: var(--white);
  @include transition3;
  .title {
    font-size: 16px;
    font-weight: 500;
    line-height: 25.6px;
    margin-bottom: 8px;
  }
  .contact-list {
    gap: 0px;
    margin-bottom: 20px;
  }
  &:hover {
    background: var(--primary-4);
  }
  &.style-2 {
    display: grid;
    gap: 15px;
    padding: 30px;
    border-color: var(--white);
  }
}
.grid-box-store {
  gap: 24px;
}
.s-store-location {
  .wg-map {
    margin-bottom: 24px;
  }
}
.s-banner-colection {
  .banner-content {
    overflow: hidden;
    border-radius: 16px;
    gap: 0px;
    background-color: #f7f7f7;
    position: relative;
  }
  .box-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0px auto;
    gap: 42px;
    padding: 24px;
  }
  .box-title-banner {
    display: grid;
    gap: 24px;
  }
  &.style-abs {
    .box-content {
      position: absolute;
      left: 15px;
      bottom: 15px;
      right: 15px;
      padding: 0;
    }
  }
  &.style-abs-2 {
    .box-content {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 15px;
      right: 15px;
      padding: 0;
    }
    .image {
      img {
        min-height: 350px;
        object-fit: cover;
      }
    }
  }
}
.banner-cls-bicycle {
  .banner-content {
    height: 400px;
    width: 100%;
  }
}
.banner-cls-electric {
  .banner-content {
    background: linear-gradient(
      67.69deg,
      #eeefff 11.66%,
      #f6f0f0 47.21%,
      #ffeff6 82.77%
    );
    padding: 16px 0px;

    .box-title-banner {
      gap: 12px;
    }
  }
}
.banner-cls-mega-electric {
  &.style-abs-2 {
    .image {
      img {
        min-height: 296px;
        object-fit: cover;
      }
    }
  }
}
.banner-cls-phonecase {
  .banner-content {
    background-color: unset;
    border-radius: unset;
    @include flex(center, space-between);
    gap: 24px;
    > * {
      width: 100%;
    }
  }
  .box-content {
    margin: unset;
    padding: 0;
  }
  .image {
    border-radius: 16px;
    overflow: hidden;
  }
}
.banner-cls-petaccess {
  .banner-content {
    background-color: #ffecce;
  }
}
.banner-cls-electric-acc {
  &.style-abs-2 {
    .box-content {
      background-color: var(--white);
      border-radius: 16px;
      left: 15px;
      right: 15px;
      margin: unset;
      padding: 30px 15px;
    }
  }
}
.banner-cls-mega-electric {
  .new {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #d2a40c;
    border-radius: 22px;
    padding: 5px 12px;
  }
  .box-title-banner {
    justify-items: center;
  }
}

.btn-sidebar-mb {
  position: fixed;
  top: 200px;
  left: 0;
  z-index: 50;
  button {
    width: 40px;
    height: 40px;
    border-radius: 0;
    border: 1px solid var(--dark);
    padding: 0;
    justify-content: center;
    &:hover {
      background-color: var(--white);
      color: var(--dark);
    }
  }
  &.right {
    left: unset;
    right: 0;
    transform: rotate(180deg);
  }
}

.s-banner-countdown {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  .image {
    img {
      min-height: 488px;
      width: 100%;
      object-fit: cover;
    }
  }
  .banner-content {
    position: absolute;
    @include topcenter;
    display: grid;
    gap: 12px;
  }
  .box-title {
    display: grid;
    gap: 4px;
  }
}
.banner-cd-phonecase {
  border-radius: 16px;
  overflow: hidden;
  margin: 0px 12px;
  .banner-wrap {
    position: relative;
  }
  .banner-content {
    z-index: 2;
    background-color: var(--white);
    border-radius: 16px;
    padding: 30px 15px;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    gap: 24px;
  }
  .countdown__timer {
    gap: 12px;
    .countdown__label {
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
    }
  }
  .box-title {
    display: grid;
    gap: 8px;
  }
  .image {
    img {
      width: 100%;
    }
  }
}
.s-banner-cd-baby {
  .banner-container {
    @include flex(center, space-between);
    border-radius: 16px;
    background-color: #f3f2ee;
    padding: 50px;
    gap: 20px;
  }
  .box-title {
    display: grid;
    gap: 12px;
  }
  .content {
    display: grid;
    gap: 24px;
  }
  .wg-countdown {
    .countdown__item {
      background-color: var(--white);
      border-radius: 50%;
      width: 70px;
      height: 70px;
      .countdown__value {
        font-family: $font-8;
        font-weight: 700;
        color: #f6620c;
      }
    }
  }
}
.s-banner-product {
  .content-banner {
    display: flex;
    gap: 42px;
    justify-content: center;
  }
  .image-wrap {
    position: relative;
  }
  .image {
    max-width: 512px;
    border-radius: 16px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .content-right {
    display: flex;
    flex-direction: column;
    gap: 42px;
    justify-content: center;
    .box-title {
      display: grid;
      gap: 10px;
    }
  }
  .loobook-product {
    position: absolute;
    left: 24px;
    right: 24px;
    bottom: 24px;
    padding: 12px;
    gap: 12px;
  }
}

.s-banner-with-text {
  .content-banner {
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 24px;
  }
  .box-title-banner {
    display: grid;
    gap: 10px;
  }
  br {
    display: none;
  }
  .image-banner {
    position: relative;
    overflow: hidden;
  }
  .image {
    border-radius: 16px;
    overflow: hidden;
    width: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .image-1 {
    &:hover {
      position: relative;
      z-index: 2;
    }
  }
}
.banner-text-fashion {
  .image-1 {
    max-width: 531px;
    margin-left: auto;
  }
  .image-2 {
    position: absolute;
    max-width: 316px;
    z-index: 2;
    left: 71px;
    bottom: 44px;
    .img-style {
      height: 100%;
    }
  }
}
.banner-text-skincare {
  &.type-2 {
    .image-1 {
      width: 85%;
    }
    .image-2 {
      position: absolute;
      width: 44%;
      bottom: 5%;
      right: 0;
    }
  }
}
.banner-text-jewelry {
  .image-1 {
    width: 85%;
  }
  .image-2 {
    width: 39%;
    position: absolute;
    z-index: 2;
    right: 0;
    bottom: 5%;
  }
}
.banner-tagline-phonecase {
  display: flex;
  gap: 50px;
  position: relative;
  .image {
    border-radius: 16px;
    overflow: hidden;
  }
  .content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .list-tagline {
    li {
      display: flex;
      gap: 10px;
      padding: 15px;
      .box-text {
        display: grid;
        gap: 5px;
      }
      &:not(:last-child) {
        border-bottom: 1px solid #59595933;
      }
    }
  }
  .icon {
    i {
      font-size: 35px;
      color: #c596ff;
    }
  }
}

.flat-title {
  display: grid;
  gap: 12px;
  text-align: center;
  margin-bottom: 30px;
  &.style-between {
    @include flex(center, space-between);
    text-align: start;
  }
  .box-title {
    display: grid;
    gap: 12px;
  }
  &.mb_1 {
    margin-bottom: 33px;
  }
  &.style-2 {
    gap: 10px;
  }
  &.style-line {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 12px;
    .line-title {
      flex-grow: 1;
      display: block;
      border-bottom: 1px solid var(--line);
    }
  }
}
.flat-title-2 {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
  .box-title {
    display: grid;
    gap: 5px;
  }
}
.simpleParallax {
  height: 100%;
  width: 100%;
  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}

.wg-offer {
  .image {
    border-radius: 16px;
    margin-bottom: 20px;
  }
  .content {
    display: grid;
    gap: 12px;
  }
  .box-title {
    display: grid;
    gap: 8px;
  }
}
.flat-title-v2 {
  display: grid;
  gap: 12px;
  margin-bottom: 30px;
}

.tab-content {
  .box-btn {
    margin-top: 24px;
  }
}

.s-banner-bundle {
  .bundle-wrap {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .tf-swiper {
    width: 100%;
  }
  .image-banner {
    width: 100%;
    height: 100%;
  }
}
.s2-banner-bundle {
  .bundle-wrap {
    position: relative;
    padding: 0px 30px 30px;
    border: 1px solid #f3a24b;
    border-radius: 16px;
  }
  .flat-title {
    background-color: var(--white);
    width: fit-content;
    margin: -25px auto 8px;
  }
  .banner-bundle {
    position: relative;
    width: 100%;
    height: 100%;
    .item {
      position: absolute;
      display: none;
      bottom: -77px;
      animation: swing2 10s infinite ease-in-out;
    }
  }
}
.bundle-wrap {
  .nav-swiper {
    width: 34px;
    height: 34px;
    &.swiper-button-prev {
      left: -40px;
    }
    &.swiper-button-next {
      right: -40px;
    }
  }
}
@keyframes swing2 {
  20% {
    transform: rotate(5deg);
  }
  40% {
    transform: rotate(-4deg);
  }
  60% {
    transform: rotate(3deg);
  }
  80% {
    transform: rotate(-2deg);
  }
  100% {
    transform: rotate(0);
  }
}
.s-banner-bundle {
  .content-list {
    display: grid;
    gap: 12px;
    padding: 24px 15px;
    border-radius: 0px 0px 16px 16px;
    border: 1px solid var(--line);
  }
  .list-recipe {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    .br--line {
      display: block;
      height: 16px;
      width: 1px;
      background-color: var(--line);
    }
  }
}

.flat-iconbox {
  padding: 33px 0px;
}

.tf-compare-table {
  border: 1px solid var(--line);
  border-radius: 16px;
  overflow-x: scroll;
  &::-webkit-scrollbar {
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 33px;
    background: #d9d9d9;
    cursor: grab;
  }
}
.tf-compare-row {
  display: flex;
}
.tf-compare-col {
  max-width: 240px;
  min-width: 240px;
  border-right: 1px solid var(--line);
  border-bottom: 1px solid var(--line);
  position: relative;
  .tf-compare-item {
    text-align: center;
    .tf-compare-image {
      aspect-ratio: calc(320 / 407);
    }
  }
}
.tf-compare-item {
  display: grid;
  gap: 15px;
  padding: 20px 15px;
  .tf-compare-image {
    border-radius: 16px;
    overflow: hidden;
    width: 100%;
    img {
      width: 100%;
      height: 100%;
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
    }
  }
  .icon-close {
    font-size: 8px;
  }
  .tf-compare-remove {
    .tf-btn-icon {
      width: 48px;
      height: 48px;
      cursor: pointer;

      &:hover {
        border-color: var(--primary);
        i {
          color: var(--primary);
          transform: unset;
        }
      }
    }
  }
  .content {
    display: grid;
    gap: 10px;
  }
}
.tf-compare-field,
.tf-compare-value {
  padding: 15px;
}
.tf-compare-value {
  @include flex(center, center);
}
.tf-compare-stock {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #48b02c;
}
.list-esd {
  .item {
    display: grid;
    gap: 10px;
    padding: 20px 0px;
    &:not(:last-child) {
      border-bottom: 1px solid var(--line);
    }
  }
}

.wrapper-wishlist {
  .tf-wishlist-empty {
    grid-column: 1 / -1;
    width: 100%;
    .text-noti {
      margin-bottom: 32px;
    }
    .btn-back-shop {
      max-width: 228px;
      font-family: $font-4;
      line-height: 120%;
      font-weight: 600;
      width: 100%;
    }
  }
}

.box-testimonial-quote {
  background-color: #eeffde;
  border-radius: 16px;
  padding: 30px 15px;
  display: grid;
  gap: 20px;
  .box-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    .avt {
      max-width: 32px;
      border-radius: 50%;
      overflow: hidden;
    }
  }
  .icon-star {
    font-size: 20px;
  }
}
.mega-categories {
  background-color: var(--white);
  text-align: left;
  color: var(--dark);
  padding: 12px 4px 22px;
  li {
    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
  .cate-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: var(--dark);
    border-radius: 4px;
    padding: 10px;
    font-size: 16px;
    line-height: 24px;
    .img {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      background-color: var(--surface);
      overflow: hidden;
    }
    &:hover,
    &.active {
      background-color: var(--purple-2);
    }
  }
}
.mega-cate-box {
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--purple);
  .categories-title {
    display: flex;
    background-color: var(--purple);
    padding: 15px 24px;
    color: var(--white);
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    text-transform: uppercase;
    .icon {
      font-size: 20px;
    }
  }
}
.mega-box {
  .mega-title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }
}
.mega-iconbox {
  border-radius: 16px;
  border: 1px solid var(--line);
  padding: 32px 12px;
  .tf-icon-box:not(:last-child) {
    margin-bottom: 32px;
  }
}

.image-compare {
  border-radius: 16px;

  .icv__arrow-wrapper {
    display: none;
  }
  .icv__circle {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.3);
    position: relative;
    border-color: transparent !important;
    &::after {
      position: absolute;
      content: "";
      display: block;
      width: 48px;
      height: 48px;
      border-radius: 999px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: var(--white);
    }
    &::before {
      position: absolute;
      font-family: $fontIcon;
      content: "\e901";
      top: 50%;
      left: 50%;
      z-index: 1;
      transform: translate(-50%, -50%);
      font-size: 14px;
    }
  }
  .icv__label {
    padding: 9px 24px;
    color: var(--dark);
    font-family: $font-2;
    border-radius: 40px;
    background-color: var(--white);
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    bottom: 24px;
  }
  .icv__label-before {
    left: 24px;
  }
  .icv__label-after {
    right: 24px;
  }
}
.img-viewer-compare-wrap {
  margin-bottom: 40px;
}
.banner-why-shop {
  padding-left: 15px;
  padding-right: 15px;
}
.s2-banner-with-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
  max-width: 1143px;
  margin: auto;
  .content-with-text {
    display: grid;
    gap: 20px;
    justify-items: left;
  }
  .box-title-content {
    display: grid;
    gap: 12px;
  }
}

.s3-banner-with-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
  .content-with-text {
    gap: 20px;
    display: grid;
    justify-items: left;
  }
  .image-banner {
    position: relative;
    overflow: hidden;
    width: 100%;
    flex-shrink: 0;
    .image {
      border-radius: 16px;
      overflow: hidden;
      width: 100%;
    }
    .image-1 {
      position: absolute;
      width: 41.6%;
      z-index: 2;
      left: 0px;
      bottom: 0px;
    }
    .image-2 {
      width: 74%;
      margin-left: auto;
    }
  }
  .box-title-content {
    .subtitle {
      margin-bottom: 10px;
      display: inline-block;
    }
    .title {
      margin-bottom: 12px;
    }
  }
}

.banner-cls-baby {
  .item {
    max-width: 217px;
    position: absolute;
    animation: moveRight 10s infinite linear alternate;
    right: -69px;
    bottom: -35px;
    display: none;
  }
}
.grid-cls-suppermarket {
  .s-cls {
    .img-style {
      width: 100%;
      height: 100%;
    }
  }
}

@keyframes moveRight {
  0% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(20px);
  }
}

.section-results {
  .results-item {
    margin-bottom: 83px;
  }
}

.section-bought-together {
  position: relative;
  h3.title {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--white);
    z-index: 2;
    padding: 0 34px;
  }
  .wrapper {
    border: 1px solid #f2bd7b;
    border-radius: 16px;
    padding: 103px 15px 82px;
    // border: 1px solid;
    // border-image: linear-gradient(180deg, #F2BD7B, #8575EA) 1;
    position: relative;
    z-index: 1;
    overflow: hidden;
    &::before {
      content: "";
      position: absolute;
      inset: 0;
      padding: 1px;
      background: linear-gradient(180deg, #f2bd7b 0%, #8575ea 100%);
      -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      border-radius: 16px;
      z-index: -1;
    }
  }
  .swiper {
    max-width: 1074px;
    margin-left: auto;
    margin-right: auto;
  }
}

.section-asked-questions {
  .content {
    display: flex;
    flex-direction: column;
    gap: 34px;
    .bot {
      display: flex;
      flex-direction: column;
      gap: 10px;
      a {
        max-width: 229px;
      }
    }
  }
  .faq-wrap {
    background-color: var(--white);
    padding: 64px 48px;
    border-radius: 20px;
    .widget-accordion {
      &:first-child {
        border-top: 0;
      }
      &:last-child {
        border-bottom: 0;
      }
    }
  }
}
