import React from "react";
import LanguageSelect from "../common/LanguageSelect";

export default function Topbar2({
  parentClass = "tf-topbar bg-blue-dermedic topbar-bg",
  fullWidth = false,
  socialMedia = {},
  marquees = [],
}) {
  return (
    <div className={parentClass}>
      <div className={fullWidth ? "container-full" : "container"}>
        <div className="topbar-wraper">
          <div className="d-none d-xl-block flex-shrink-0">
            <ul className="topbar-left tf-social-icon">
              {socialMedia.facebook && (
                <li>
                  <a
                    href={socialMedia.facebook}
                    className="social-item social-facebook"
                  >
                    <i className="icon icon-fb" />
                  </a>
                </li>
              )}
              {socialMedia.instagram && (
                <li>
                  <a
                    href={socialMedia.instagram}
                    className="social-item social-instagram"
                  >
                    <i className="icon icon-instagram" />
                  </a>
                </li>
              )}
              {socialMedia.twitter && (
                <li>
                  <a
                    href={socialMedia.twitter}
                    className="social-item social-x"
                  >
                    <i className="icon icon-x" />
                  </a>
                </li>
              )}
              {socialMedia.youtube && (
                <li>
                  <a href={socialMedia.youtube}
                    className="social-item social-youtube">
                    <i className="icon icon-youtube" />
                  </a>
                </li>
              )}
            </ul>
          </div>
          <div className="overflow-hidden">
            <div className="topbar-center marquee-wrapper">
              <div className="initial-child-container">
                {/* Repeat marquee items 5 times for continuous scroll effect */}
                {[1, 2, 3, 4, 5].map((groupIndex) => (
                  <React.Fragment key={groupIndex}>
                    {marquees.map((item, index) => (
                      <React.Fragment key={`${groupIndex}-${index}`}>
                        <div className="marquee-child-item">
                          <p>{item}</p>
                        </div>
                        <div className="marquee-child-item">
                          <span className="dot" />
                        </div>
                      </React.Fragment>
                    ))}
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>
          {/* <div className="d-none d-xl-block flex-shrink-0"> */}
          {/*   <div className="topbar-right"> */}
          {/*     <div className="tf-languages"> */}
          {/*       <LanguageSelect topStart /> */}
          {/*     </div> */}
          {/*   </div> */}
          {/* </div> */}
        </div>
      </div>
    </div>
  );
}
