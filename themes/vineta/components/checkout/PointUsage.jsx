"use client";

import { useState, useEffect } from 'react';
import { formatTLPrice } from '@/utils/currency';
import { useCustomerPoints } from '@/hooks/useCustomerPoints';

export default function PointUsage({
  orderSummary,
  onPointsChange,
  maxUsablePercentage = 50
}) {
  const { pointBalance, loading, error } = useCustomerPoints();
  const [pointsToUse, setPointsToUse] = useState(0);

  // Maksimum kullanılabilir puan miktarını hesapla
  const maxUsableAmount = Math.floor(orderSummary.totalAmount * (maxUsablePercentage / 100));
  const maxUsablePoints = Math.min(pointBalance, maxUsableAmount);

  // Puan kullanımı değiştiğinde parent component'e bildir
  useEffect(() => {
    onPointsChange(pointsToUse);
  }, [pointsToUse, onPointsChange]);

  // <PERSON>uan miktarı değişikliği
  const handlePointsChange = (value) => {
    const numValue = parseInt(value) || 0;
    const clampedValue = Math.min(Math.max(0, numValue), maxUsablePoints);
    setPointsToUse(clampedValue);
  };

  // Tümünü kullan
  const handleUseAllPoints = () => {
    setPointsToUse(maxUsablePoints);
  };

  // Sıfırla
  const handleResetPoints = () => {
    setPointsToUse(0);
  };

  if (loading) {
    return (
      <div className="cart-box mb-3">
        <div className="title text-lg fw-medium">Puan Kullanımı</div>
        <div className="text-center p-3">
          <div className="spinner-border spinner-border-sm" role="status">
            <span className="visually-hidden">Yükleniyor...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return null; // Hata varsa veya puan yoksa component'i gösterme
  }

  return (
    <div className="cart-box mb-3">
      <div className="title text-lg fw-medium">Puan Kullanımı</div>

      <div className="mt-3">
        <p className="text-sm text-muted mb-2">
          Mevcut Puan Bakiyeniz: <strong>{pointBalance.toLocaleString('tr-TR')} puan</strong>
        </p>
        {pointBalance > 0 && (
          <div className="point-usage-controls">
            <div className="mb-3">
              <label className="form-label text-sm">Kullanmak istediğiniz puan miktarı:</label>
              <input
                type="number"
                className="form-control"
                value={pointsToUse}
                onChange={(e) => handlePointsChange(e.target.value)}
                min="0"
                max={maxUsablePoints}
                placeholder="0"
              />
              <small className="text-muted mt-1 d-block">
                Maksimum {maxUsablePoints.toLocaleString('tr-TR')} puan kullanabilirsiniz (Sepet tutarınızın yarısını geçemez)
              </small>
            </div>

            <div className="d-flex gap-2 mb-3">
              <button
                type="button"
                className="tf-btn btn-blue-2 animate-btn"
                onClick={handleUseAllPoints}
                disabled={maxUsablePoints === 0}
              >
                Tümünü Kullan
              </button>
              <button
                type="button"
                className="tf-btn btn-red btn-sm"
                onClick={handleResetPoints}
                disabled={pointsToUse === 0}
              >
                Sıfırla
              </button>
            </div>

            {pointsToUse > 0 && (
              <div className="point-discount-info p-2 bg-light rounded">
                <small className="text-success">
                  <strong>{pointsToUse.toLocaleString('tr-TR')} puan</strong> kullanarak{' '}
                  <strong>{formatTLPrice(pointsToUse)}</strong> indirim alacaksınız.
                </small>
              </div>
            )}
          </div>
        )}
        {pointBalance === 0 && (
          <p className="text-sm text-muted">
            Mevcut puan bakiyeniz bulunmamaktadır. Puan kazanmak için alışverişinizi tamamlamalısınız.
          </p>
        )}
      </div>
    </div>
  );
}
