"use client";

import "@/public/scss/main.scss";
import "photoswipe/dist/photoswipe.css";
import "rc-slider/assets/index.css";
import { useEffect, useState } from "react";
import { SessionProvider } from "next-auth/react";

import Compare from "@/components/modals/Compare";
import Login from "@/components/modals/Login";
import Register from "@/components/modals/Register";
import MobileMenu from "@/components/modals/MobileMenu";
import Quickview from "@/components/modals/Quickview";
import SearchModal from "@/components/modals/SearchModal";
import Toolbar from "@/components/modals/Toolbar";
import Context from "@/context/Context";
import ScrollTop from "@/components/common/ScrollTop";
import { usePathname } from "next/navigation";
import QuestionModal from "@/components/modals/QuestionModal";
import ShareModal from "@/components/modals/ShareModal";
import BeforeLeave from "@/components/modals/BeforeLeave";
import { getCategories } from "@/services/category";
import { getMarqueeTexts } from "@/services/systemSettings";
import CartComponent from "@/components/modals/CartComponent";
import DbSidebar from "@/components/modals/DbSidebar";
import RtlToggler from "@/components/common/RtlToggler";
import { headerData } from "@/data/header-products";
import Header1 from "@/components/headers/Header1";
import Header5 from "@/components/headers/Header5";
import Topbar2 from "@/components/headers/Topbar2";
import Footer2 from "@/components/footers/Footer2";
import { injectCategories } from "@/utils/injectCategories";
import { Toaster } from "sonner";
import { FilterProvider } from "@/contexts/FilterContext";

export default function RootLayout({ children, params: { locale } }) {
  const pathname = usePathname();
  const [productsData, setProductsData] = useState(headerData);
  const [marqueeTexts, setMarqueeTexts] = useState(headerData.marquees);
  /* console.log("Current Pathname:", pathname); */
  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      try {
        // Load categories
        const categoriesResponse = await getCategories();
        if (isMounted) {
          setProductsData(prev => injectCategories(prev, categoriesResponse));
        }

        // Load marquee texts from SystemSettings
        const marqueeResponse = await getMarqueeTexts();
        if (isMounted && marqueeResponse && marqueeResponse.length > 0) {
          setMarqueeTexts(marqueeResponse);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        // Keep default values on error
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [])
  useEffect(() => {

    if (typeof window !== "undefined") {
      // Import the script only on the client side
      import("bootstrap/dist/js/bootstrap.esm").then(() => {
        // Module is imported, you can access any exported functionality if
      });
    }
  }, []);
  useEffect(() => {
    let lastScrollTop = 0;
    const delta = 5;
    let navbarHeight = 0;
    let didScroll = false;
    const header = document.querySelector("header");

    const handleScroll = () => {
      didScroll = true;
    };

    const checkScroll = () => {
      if (didScroll && header) {
        const st = window.scrollY || document.documentElement.scrollTop;
        navbarHeight = header.offsetHeight;

        if (st > navbarHeight) {
          if (st > lastScrollTop + delta) {
            // Scroll down
            header.style.top = `-${navbarHeight}px`;
          } else if (st < lastScrollTop - delta) {
            // Scroll up
            header.style.top = "0";
            header.classList.add("header-bg");
          }
        } else {
          // At top of page
          header.style.top = "";
          header.classList.remove("header-bg");
        }

        lastScrollTop = st;
        didScroll = false;
      }
    };

    // Initial measurement
    if (header) {
      navbarHeight = header.offsetHeight;
    }

    // Set up event listeners
    window.addEventListener("scroll", handleScroll);
    const scrollInterval = setInterval(checkScroll, 250);

    // Cleanup function
    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearInterval(scrollInterval);
    };
  }, [pathname]); // Empty dependency array means this runs once on mount

  useEffect(() => {
    // Close any open modal and offcanvas when route changes
    const closeModalsAndOffcanvas = async () => {
      try {
        const bootstrap = await import("bootstrap");

        const modalElements = document.querySelectorAll(".modal.show");
        modalElements.forEach((modal) => {
          const modalInstance = bootstrap.Modal.getInstance(modal);
          if (modalInstance) {
            modalInstance.hide();
          }
        });

        const offcanvasElements = document.querySelectorAll(".offcanvas.show");
        offcanvasElements.forEach((offcanvas) => {
          const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvas);
          if (offcanvasInstance) {
            offcanvasInstance.hide();
          }
        });
      } catch (error) {
        console.error('Bootstrap import hatası:', error);
      }
    };

    closeModalsAndOffcanvas();
  }, [pathname]); // Runs every time the route changes
  useEffect(() => {
    const WOW = require("@/utils/wow");
    const wow = new WOW.default({
      mobile: false,
      live: false,
    });
    wow.init();
  }, [pathname]);
  return (
    <html lang="tr">
      <body>
        <SessionProvider>
          <FilterProvider>
          <Context>
            <Topbar2 socialMedia={productsData.contact.socialMedia} marquees={marqueeTexts} />
            {pathname !== "/" ? <Header1 productsMenu={productsData.productsMenu} /> : <Header5 productsMenu={productsData.productsMenu} />}
            {children}
            <CartComponent />
            <Compare />
            <Login />
            <Register />
            <MobileMenu />
            <Quickview />
            <SearchModal />
            <Toolbar />
            <QuestionModal />
            <ShareModal />
            <DbSidebar />
            <Footer2 logo={productsData.logoDark.imgSrc} contact={productsData.contact} />
          </Context>
          <ScrollTop />
          {/* <BeforeLeave /> */}
            <Toaster richColors position="top-center" />
            </FilterProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
